import ValidatedCreatorCodesHttpClient from "../../../../src/server/creatorCode/ValidatedCreatorCodesHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

describe("ValidatedCreatorCodesHttpClient", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("validates a creator code", async () => {
    const response = {
      data: {
        creatorCode: "TEST123",
        creatorId: "creator-456",
        isValidCode: true
      }
    };

    const client = { get: jest.fn().mockResolvedValue(response) } as unknown as TraceableHttpClient;
    const registrationCodes = new ValidatedCreatorCodesHttpClient(client);

    const result = await registrationCodes.validateCreatorCode("TEST123", "creator-456");

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/v2/creator-code/TEST123", {
      query: { creatorId: "creator-456" }
    });
    expect(result.isValidCode).toBe(true);
    expect(result.creatorCode).toBe("TEST123");
    expect(result.creatorId).toBe("creator-456");
  });
});
