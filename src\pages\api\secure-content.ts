import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import VerifyContentUrlController from "../../server/verifyContentUrl/VerifyContentUrlController";
import session from "@src/middleware/Session";
import onError from "@src/middleware/OnError";
import ApiContainer from "@src/ApiContainer";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflight";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(addTelemetryInformation)
  .post(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(VerifyContentUrlController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
