import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import VerifyContentUrlController from "@src/server/verifyContentUrl/VerifyContentUrlController";
import ContentScanningHttpClient from "@src/server/creatorCode/ContentScanningHttpClient";

describe("VerifyContentUrlController", () => {
  const options: RequestHandlerOptions = {};

  beforeEach(() => jest.clearAllMocks());

  it("verify content urls successfully", async () => {
    const urls = ["https://reddit.com", "https://google.com"];
    const body = { urls: { urls } };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `/v1/secure-content/`,
      body
    });
    const mockVerifyContentUrl = jest.fn().mockResolvedValue([
      { url: "https://reddit.com", isSecure: true },
      { url: "https://google.com", isSecure: true }
    ]);
    const controller = new VerifyContentUrlController(options, {
      verifyUrls: mockVerifyContentUrl
    } as unknown as ContentScanningHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);
    expect(mockVerifyContentUrl).toHaveBeenCalledWith({ urls }, undefined);
  });

  it("verify content urls successfully with type 'CREATORS'", async () => {
    const urls = ["https://reddit.com"];
    const type = "CREATORS";
    const body = { urls: { urls } };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `/api/verify-content-url`,
      body,
      query: { type }
    });
    const mockVerifyContentUrl = jest.fn().mockResolvedValue([
      { url: "https://reddit.com", isSecure: true }
    ]);
    const controller = new VerifyContentUrlController(options, {
      verifyUrls: mockVerifyContentUrl
    } as unknown as ContentScanningHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);
    expect(mockVerifyContentUrl).toHaveBeenCalledWith({ urls }, "CREATORS");
  });
});
