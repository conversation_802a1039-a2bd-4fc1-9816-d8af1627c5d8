import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import VerifyContentUrlController from "@src/server/verifyContentUrl/VerifyContentUrlController";
import ContentScanningHttpClient from "@src/server/creatorCode/ContentScanningHttpClient";

describe("VerifyContentUrlController", () => {
  const options: RequestHandlerOptions = {};

  beforeEach(() => jest.clearAllMocks());

  it("verify content urls successfully", async () => {
    const urls = ["https://reddit.com", "https://google.com"];
    const resolvedContentUrls = [
      { url: "https://reddit.com", isSecure: true },
      { url: "https://google.com", isSecure: true }
    ];
    const body = { urls: { urls } };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `/v1/secure-content/`,
      body
    });
    const mockResolvedContentUrls = jest.fn().mockResolvedValue(resolvedContentUrls);
    const controller = new VerifyContentUrlController(options, {
      verifyUrls: mockResolvedContentUrls
    } as unknown as ContentScanningHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockResolvedContentUrls).toHaveBeenCalledTimes(1);
    expect(mockResolvedContentUrls).toHaveBeenCalledWith({ urls }, undefined);
  });
});
