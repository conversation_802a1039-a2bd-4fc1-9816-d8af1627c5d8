import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import ContentScanningHttpClient from "@src/server/creatorCode/ContentScanningHttpClient";

describe("ContentScanningHttpClient", () => {
  beforeEach(() => jest.clearAllMocks());
  it("checks if provided content URLs are in the allow list", async () => {
    const scanResult = {
      results: [
        {
          url: "https://www.reddit.com",
          isSecure: true
        }
      ]
    };
    const client = { post: jest.fn().mockResolvedValue({ data: scanResult }) };
    const contentURLs = { urls: ["https://reddit.com"] };
    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);

    const application = await applications.verifyUrls(contentURLs, "INTERESTED_CREATORS");

    expect(application).toEqual(scanResult);
    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v1/secure-content", {
      body: { urls: contentURLs },
      query: { type: "INTERESTED_CREATORS" }
    });
  });
});
