import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type UnreviewedCreatorCode = {
  programCode: string;
  deviceType: string;
  platform: string;
  nucleusId: number;
  contents: Array<string>;
};

export type ScannedCreatorCode = {
  content: string;
  healthy: boolean;
};

export type ContentScanResult = {
  results: Array<ScannedCreatorCode>;
};

export type ContentUrls = {
  urls: Array<string>;
};

type ScannedUrl = {
  url: string;
  isSecure: boolean;
};

type UrlScanResult = {
  results: Array<ScannedUrl>;
};

export type ScanType = "INTERESTED_CREATORS" | "CREATORS";

@Service()
class ContentScanningHttpClient {
  constructor(@Inject("contentScanningClient") private readonly client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/checkContentHealth Validate Content Creator Code}
   */
  async verifyCreatorCode(creatorCodeRequestBody: UnreviewedCreatorCode): Promise<ContentScanResult> {
    const response = await this.client.post("/v1/healthy-text-contents", { body: creatorCodeRequestBody });
    return response.data;
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#operation/validateContentUrl Validate Content URL}
   */
  async verifyUrls(urls: ContentUrls, type: ScanType): Promise<UrlScanResult> {
    const response = await this.client.post("/v1/secure-content", {
      body: { urls },
      query: { type }
    });
    return response.data;
  }
}

export default ContentScanningHttpClient;
